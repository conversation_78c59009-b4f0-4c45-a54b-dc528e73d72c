import { Request, Response } from "express";
import {
  mailchimpWebhookEvents,
  MailchimpWebhookNotificationEventType
} from "../event-handlers/notificationEvents";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import NotificationService from "../services/notificationService";
import NotificationSettingsService from "../services/notificationSettingsService";
import { EmailNotificationSettingEnum } from "../models/NotificationSettings";
import qs from "qs";
import { AudienceIdEnum } from "../external-services/mailchimpService";

interface MailchimpWebhookProperties {
  [key: string]: any;
}

// Mailchimp audience webhook types
interface MailchimpAudienceWebhookData {
  type: "subscribe" | "unsubscribe" | "profile" | "upemail" | "cleaned" | "campaign";
  fired_at: string;
  data: {
    id?: string;
    list_id?: AudienceIdEnum;
    email: string;
    email_type?: string;
    ip_opt?: string;
    ip_signup?: string;
    action?: string;
    reason?: string;
    campaign_id?: string;
    merges?: {
      EMAIL?: string;
      FNAME?: string;
      LNAME?: string;
      [key: string]: any;
    };
  };
}

const AUDIENCE_EMAIL_NOTIFICATION_SETTING_MAPPING: Record<AudienceIdEnum, EmailNotificationSettingEnum> = {
  [AudienceIdEnum.WEALTHYHOOD]: EmailNotificationSettingEnum.PROMOTIONAL,
  [AudienceIdEnum.WEALTHYBITES]: EmailNotificationSettingEnum.WEALTHYBITES
};

export default class MailchimpWebhookController {
  public static async processWebhook(req: Request, res: Response): Promise<Response> {
    const body = req.body;
    const headers = req.headers;

    const { notification, email, properties } = body as {
      notification: MailchimpWebhookNotificationEventType;
      email: string;
      properties: MailchimpWebhookProperties;
    };

    logger.info(`Received mailchimp webhook for notification ${notification} & user ${email}`, {
      module: "MailchimpWebhookController",
      method: "processWebhook",
      data: {
        body,
        headers
      }
    });

    if (!mailchimpWebhookEvents.includes(notification)) {
      logger.error(`Received mailchimp webhook for invalid notification ${notification}`, {
        module: "MailchimpWebhookController",
        method: "processWebhook",
        data: {
          body,
          headers
        }
      });
      return res.sendStatus(403);
    }

    const user = await User.findOne({ email });

    if (!user) {
      logger.error(`Received mailchimp webhook for email ${email} but could not retrieve user`, {
        module: "MailchimpWebhookController",
        method: "processWebhook",
        data: {
          body,
          headers
        }
      });
      return res.sendStatus(403);
    }

    await NotificationService.createAppNotification(
      user.id,
      { notificationId: notification, properties: new Map(Object.entries(properties ?? {})) },
      { sendImmediately: true }
    );

    return res.sendStatus(204);
  }

  /**
   * Processes Mailchimp audience webhooks (subscribe, unsubscribe, etc.)
   */
  public static async processAudienceWebhook(req: Request, res: Response): Promise<Response> {
    const body = qs.parse(req.body) as unknown as MailchimpAudienceWebhookData;

    logger.info(`Received mailchimp audience webhook for type ${body.type} & user ${body.data.email}`, {
      module: "MailchimpWebhookController",
      method: "processAudienceWebhook",
      data: {
        body
      }
    });

    // Handle unsubscribe events
    if (body.type === "unsubscribe") {
      return await MailchimpWebhookController._handleUnsubscribe(body, res);
    }

    // For other audience events (subscribe, profile, etc.), we just log and return success
    logger.info(`Received mailchimp audience webhook for type ${body.type} - no action needed`, {
      module: "MailchimpWebhookController",
      method: "processAudienceWebhook",
      data: {
        type: body.type,
        email: body.data.email
      }
    });

    return res.sendStatus(204);
  }

  /**
   * Handles Mailchimp unsubscribe events by updating user's email notification settings
   */
  private static async _handleUnsubscribe(body: MailchimpAudienceWebhookData, res: Response): Promise<Response> {
    const { email, reason, action, list_id } = body.data;

    logger.info(`Processing mailchimp unsubscribe for user ${email}`, {
      module: "MailchimpWebhookController",
      method: "_handleUnsubscribe",
      data: {
        email,
        reason,
        action,
        list_id
      }
    });

    const user = await User.findOne({ email });

    if (!user) {
      logger.warn(`Received mailchimp unsubscribe for email ${email} but could not find user`, {
        module: "MailchimpWebhookController",
        method: "_handleUnsubscribe",
        data: {
          email,
          reason,
          action,
          list_id
        }
      });
      // Return success even if user not found - this is normal for users who may have been deleted
      return res.sendStatus(204);
    }

    try {
      await NotificationSettingsService.updateNotificationSetting(
        user,
        AUDIENCE_EMAIL_NOTIFICATION_SETTING_MAPPING[list_id],
        false
      );

      logger.info(`Successfully updated email notification settings for unsubscribed user ${email}`, {
        module: "MailchimpWebhookController",
        method: "_handleUnsubscribe",
        data: {
          email,
          reason,
          action,
          userId: user.id
        }
      });
    } catch (error) {
      logger.error(`Failed to update notification settings for unsubscribed user ${email}`, {
        module: "MailchimpWebhookController",
        method: "_handleUnsubscribe",
        data: {
          email,
          reason,
          action,
          userId: user.id,
          error
        }
      });

      return res.sendStatus(403);
    }

    return res.sendStatus(204);
  }
}
