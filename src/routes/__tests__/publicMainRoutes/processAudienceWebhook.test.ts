import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import request from "supertest";
import app from "../../../app";
import { buildNotificationSettings, buildUser } from "../../../tests/utils/generateModels";
import { NotificationSettings } from "../../../models/NotificationSettings";
import { EmailNotificationSettingEnum } from "../../../models/NotificationSettings";
import { faker } from "@faker-js/faker";
import { AudienceIdEnum } from "../../../external-services/mailchimpService";
import { UserDocument } from "../../../models/User";
import logger from "../../../external-services/loggerService";

const createMailchimpPayload = (
  type: string = "unsubscribe",
  email: string = faker.internet.email(),
  listId: AudienceIdEnum = AudienceIdEnum.WEALTHYHOOD
) => {
  return {
    type,
    "data[email]": email,
    "data[list_id]": listId
  };
};

describe("publicMainRoutes.mailchimpWebhook.processAudienceWebhook", () => {
  beforeAll(async () => {
    await connectDb("processAudienceWebhook");
  });
  afterAll(async () => {
    await closeDb();
  });
  afterEach(async () => await clearDb());

  describe("POST /mailchimp/webhooks/audience", () => {
    describe("when processing audience unsubscribe events", () => {
      describe("when we receive a unsubscribe event from the wealthyhood audience", () => {
        let user: UserDocument;

        beforeEach(async () => {
          user = await buildUser();
          await buildNotificationSettings({ owner: user.id });
        });

        it("should update user notification settings for promotional emails", async () => {
          const mailchimpPayload = createMailchimpPayload("unsubscribe", user.email, AudienceIdEnum.WEALTHYHOOD);

          const response = await request(app)
            .post("/mailchimp/webhooks/audience")
            .send(mailchimpPayload)
            .set("Accept", "application/json");

          expect(response.status).toEqual(204);

          // Check that notification settings were updated
          const updatedSettings = await NotificationSettings.findOne({ owner: user._id });
          expect(updatedSettings?.email.settings.get(EmailNotificationSettingEnum.PROMOTIONAL)).toBe(false);
          expect(updatedSettings?.email.settings.get(EmailNotificationSettingEnum.WEALTHYBITES)).toBe(true);
        });
      });

      describe("when we receive a unsubscribe event from the wealthybites audience", () => {
        let user: UserDocument;

        beforeEach(async () => {
          user = await buildUser();
          await buildNotificationSettings({ owner: user.id });
        });

        it("should update user notification settings for wealthybites emails", async () => {
          const mailchimpPayload = createMailchimpPayload("unsubscribe", user.email, AudienceIdEnum.WEALTHYBITES);

          const response = await request(app)
            .post("/mailchimp/webhooks/audience")
            .send(mailchimpPayload)
            .set("Accept", "application/json");

          expect(response.status).toEqual(204);

          // Check that notification settings were updated
          const updatedSettings = await NotificationSettings.findOne({ owner: user._id });
          expect(updatedSettings?.email.settings.get(EmailNotificationSettingEnum.WEALTHYBITES)).toBe(false);
          expect(updatedSettings?.email.settings.get(EmailNotificationSettingEnum.PROMOTIONAL)).toBe(true);
        });
      });

      describe("when we receive a unsubscribe event from the wealthybites audience, but the user doesn't exist", () => {
        it("should return status 204 and log a warning", async () => {
          const mailchimpPayload = createMailchimpPayload();

          const response = await request(app)
            .post("/mailchimp/webhooks/audience")
            .send(mailchimpPayload)
            .set("Accept", "application/json");

          expect(response.status).toEqual(204);

          expect(logger.warn).toHaveBeenCalledWith(
            expect.stringContaining(
              `Received mailchimp unsubscribe for email ${mailchimpPayload["data[email]"]} but could not find user`
            ),
            expect.objectContaining({
              module: "MailchimpWebhookController",
              method: "_handleUnsubscribe"
            })
          );
        });
      });
    });
  });
});
